using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Timers;
using log4net;
using OfficeOpenXml;
using LicenseContext = OfficeOpenXml.LicenseContext;

namespace FlowCheck_DPLNG
{
    /// <summary>
    /// 自动核查服务类 - 独立模块，不影响现有UI流程
    /// 实现定时自动核查所有站点、流量计、流量计算机的功能
    /// </summary>
    public class AutomaticCheckService : IDisposable
    {
        private static readonly ILog Log = LogManager.GetLogger(typeof(AutomaticCheckService));
        
        private readonly string _connectionString;
        private readonly string _reportFolderPath;
        private readonly string _stationTableName;
        private readonly StationDataManager _stationManager;
        private readonly StationConfigManager _configManager;
        private readonly CheckListDataService _checkListDataService;
        
        // 添加XML配置读取器
        private readonly XmlConfigReader _xmlConfigReader;
        
        private Timer _dailyTimer;
        private bool _isRunning = false;
        private bool _disposed = false;
        
        // 自动核查配置
        private readonly AutoCheckConfig _config;
        private readonly string _fileNamePrefix;
        
        // AGA10算法DLL导入
        [DllImport(@"Lib\234dll.dll", EntryPoint = "?AGA10_Init@@YGHXZ", ExactSpelling = true, CharSet = CharSet.Ansi, SetLastError = true)]
        private static extern long AGA10_Init();
        
        [DllImport(@"Lib\234dll.dll", EntryPoint = "?AGA10_UnInit@@YGHXZ", ExactSpelling = true, CharSet = CharSet.Ansi, SetLastError = true)]
        private static extern long AGA10_UnInit();
        
        [DllImport(@"Lib\234dll.dll", EntryPoint = "?Crit@@YGNPAUtagAGA10STRUCT@@N@Z", CallingConvention = CallingConvention.StdCall)]
        private static extern double Crit(ref AGA10STRUCT AGAPtr, double dPlenumVelocity);

        // 添加线程安全锁，确保AGA10 DLL调用的线程安全
        private static readonly object _aga10Lock = new object();

        // 字段映射相关的属性和方法（从Form1移植）
        private Dictionary<string, string> fieldMappings = new Dictionary<string, string>();
        private string historyTagSuffix = "_1";

        /// <summary>
        /// 初始化字段映射（根据设备类型）
        /// </summary>
        private void InitializeFieldMappings(bool isRMGDevice)
        {
            fieldMappings.Clear();
            
            if (isRMGDevice)
            {
                // RMG设备字段映射
                fieldMappings["StatusA"] = "P1Status";
                fieldMappings["StatusB"] = "P2Status";
                fieldMappings["StatusC"] = "P3Status";
                fieldMappings["StatusD"] = "P4Status";
                fieldMappings["SystemStatus"] = "SystemStatus";
                
                Log.Info("Initialized RMG device field mappings");
            }
            else
            {
                // DANIEL GUSM设备使用默认映射
                fieldMappings["StatusA"] = "StatusA";
                fieldMappings["StatusB"] = "StatusB";
                fieldMappings["StatusC"] = "StatusC";
                fieldMappings["StatusD"] = "StatusD";
                fieldMappings["SystemStatus"] = "SystemStatus";
                
                Log.Info("Initialized DANIEL GUSM device field mappings");
            }
        }

        /// <summary>
        /// 获取映射后的字段名
        /// </summary>
        private string GetMappedFieldName(string originalFieldName)
        {
            if (fieldMappings.ContainsKey(originalFieldName))
            {
                return fieldMappings[originalFieldName];
            }
            return originalFieldName;
        }

        /// <summary>
        /// 获取映射字段的数值（简化版本）
        /// </summary>
        private double GetMappedFieldValueSimple(Dictionary<string, string> dataDict, string fieldName)
        {
            try
            {
                var mappedFieldName = GetMappedFieldName(fieldName);
                
                if (dataDict.TryGetValue(mappedFieldName, out string value))
                {
                    if (double.TryParse(value, out double result))
                    {
                        return result;
                    }
                }
                
                Log.Debug($"Failed to get mapped field value for: {fieldName} -> {mappedFieldName}");
                return 0.0;
            }
            catch (Exception ex)
            {
                Log.Error($"Error getting mapped field value for {fieldName}: {ex.Message}", ex);
                return 0.0;
            }
        }

        /// <summary>
        /// 获取RMG特定字段的数值（简化版本）
        /// </summary>
        private double GetRMGSpecificFieldValueSimple(Dictionary<string, string> dataDict, string rmgFieldName)
        {
            try
            {
                if (dataDict.TryGetValue(rmgFieldName, out string value))
                {
                    if (double.TryParse(value, out double result))
                    {
                        return result;
                    }
                }
                
                Log.Debug($"Failed to get RMG specific field value for: {rmgFieldName}");
                return 0.0;
            }
            catch (Exception ex)
            {
                Log.Error($"Error getting RMG specific field value for {rmgFieldName}: {ex.Message}", ex);
                return 0.0;
            }
        }

        /// <summary>
        /// 解析数值，失败时返回默认值
        /// </summary>
        private double ParseDoubleOrDefault(string value, double defaultValue = 0)
        {
            if (double.TryParse(value, out double result))
            {
                return result;
            }
            return defaultValue;
        }

        /// <summary>
        /// 获取字典中的值，如果键不存在则返回默认值（兼容老版本.NET Framework）
        /// </summary>
        private string GetValueOrDefault(Dictionary<string, string> dictionary, string key, string defaultValue = "")
        {
            return dictionary.ContainsKey(key) ? dictionary[key] : defaultValue;
        }

        /// <summary>
        /// 计算总组分（对应Form1中的CalTotComp方法）
        /// </summary>
        private double CalculateTotalComposition(Dictionary<string, string> fcData)
        {
            try
            {
                var totalComp = 0.0;
                
                totalComp += ParseDoubleOrDefault(GetValueOrDefault(fcData, "SLCT_METHANE" + historyTagSuffix, "0"));
                totalComp += ParseDoubleOrDefault(GetValueOrDefault(fcData, "SLCT_ETHANE" + historyTagSuffix, "0"));
                totalComp += ParseDoubleOrDefault(GetValueOrDefault(fcData, "SLCT_PROPANE" + historyTagSuffix, "0"));
                totalComp += ParseDoubleOrDefault(GetValueOrDefault(fcData, "SLCT_N_BUTANE" + historyTagSuffix, "0"));
                totalComp += ParseDoubleOrDefault(GetValueOrDefault(fcData, "SLCT_I_BUTANE" + historyTagSuffix, "0"));
                totalComp += ParseDoubleOrDefault(GetValueOrDefault(fcData, "SLCT_N_PENTANE" + historyTagSuffix, "0"));
                totalComp += ParseDoubleOrDefault(GetValueOrDefault(fcData, "SLCT_I_PENTANE" + historyTagSuffix, "0"));
                totalComp += ParseDoubleOrDefault(GetValueOrDefault(fcData, "SLCT_NEO_PENTANE" + historyTagSuffix, "0"));
                totalComp += ParseDoubleOrDefault(GetValueOrDefault(fcData, "SLCT_HEXANE" + historyTagSuffix, "0"));
                totalComp += ParseDoubleOrDefault(GetValueOrDefault(fcData, "SLCT_NITROGEN" + historyTagSuffix, "0"));
                totalComp += ParseDoubleOrDefault(GetValueOrDefault(fcData, "SLCT_CO2" + historyTagSuffix, "0"));
                
                Log.Info($"Total composition calculated: {totalComp:F4}%");
                return totalComp;
            }
            catch (Exception ex)
            {
                Log.Error($"Error calculating total composition: {ex.Message}", ex);
                return 0.0;
            }
        }

        /// <summary>
        /// 验证系统状态（对应Form1中的状态检查逻辑）
        /// </summary>
        private bool VerifySystemStatus(Dictionary<string, string> ftData, bool isRMGDevice)
        {
            try
            {
                bool isSystemStatusGood = false;
                
                if (isRMGDevice)
                {
                    // RMG设备：P1-P6状态检查
                    var statusP1 = GetMappedFieldValueSimple(ftData, "StatusA"); // → P1Status
                    var statusP2 = GetMappedFieldValueSimple(ftData, "StatusB"); // → P2Status
                    var statusP3 = GetMappedFieldValueSimple(ftData, "StatusC"); // → P3Status
                    var statusP4 = GetMappedFieldValueSimple(ftData, "StatusD"); // → P4Status
                    var statusP5 = GetRMGSpecificFieldValueSimple(ftData, "P5Fault");
                    var statusP6 = GetRMGSpecificFieldValueSimple(ftData, "P6Fault");

                    isSystemStatusGood = new[] { statusP1, statusP2, statusP3, statusP4, statusP5, statusP6 }.All(d => d == 0);
                    
                    Log.Info($"RMG System Status - P1:{statusP1}, P2:{statusP2}, P3:{statusP3}, P4:{statusP4}, P5:{statusP5}, P6:{statusP6} => Good:{isSystemStatusGood}");
                }
                else
                {
                    // DANIEL GUSM设备
                    var statusA = GetMappedFieldValueSimple(ftData, "StatusA");
                    var statusB = GetMappedFieldValueSimple(ftData, "StatusB");
                    var statusC = GetMappedFieldValueSimple(ftData, "StatusC");
                    var statusD = GetMappedFieldValueSimple(ftData, "StatusD");
                    var systemStatus = GetMappedFieldValueSimple(ftData, "SystemStatus");

                    isSystemStatusGood = systemStatus == 0;
                    
                    Log.Info($"DANIEL System Status - A:{statusA}, B:{statusB}, C:{statusC}, D:{statusD}, System:{systemStatus} => Good:{isSystemStatusGood}");
                }
                
                return isSystemStatusGood;
            }
            catch (Exception ex)
            {
                Log.Error($"Error verifying system status: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取关键测量值（用于日志记录和验证）
        /// </summary>
        private void LogKeyMeasurements(Dictionary<string, string> fcData, Dictionary<string, string> ftData, bool isRMGDevice)
        {
            try
            {
                // 气体组分
                var methane = ParseDoubleOrDefault(GetValueOrDefault(fcData, "SLCT_METHANE" + historyTagSuffix, "0"));
                var nitrogen = ParseDoubleOrDefault(GetValueOrDefault(fcData, "SLCT_NITROGEN" + historyTagSuffix, "0"));
                var co2 = ParseDoubleOrDefault(GetValueOrDefault(fcData, "SLCT_CO2" + historyTagSuffix, "0"));
                
                // 温度和压力
                var temperature = ParseDoubleOrDefault(GetValueOrDefault(fcData, "TempInuse" + historyTagSuffix, "0"));
                var pressure = ParseDoubleOrDefault(GetValueOrDefault(fcData, "PressInuse" + historyTagSuffix, "0"));
                
                // 声速
                string sosFieldName = isRMGDevice ? "USMAvgVOSInuse" + historyTagSuffix : "USMAvgVOS" + historyTagSuffix;
                var measuredSOS = ParseDoubleOrDefault(GetValueOrDefault(fcData, sosFieldName, "0"));
                
                // 管径（仅DANIEL设备）
                var intDiam = isRMGDevice ? 0.0 : ParseDoubleOrDefault(GetValueOrDefault(fcData, "SpoolIntDia" + historyTagSuffix, "0"));
                
                Log.Info($"Key Measurements - CH4:{methane:F2}%, N2:{nitrogen:F2}%, CO2:{co2:F2}%");
                Log.Info($"Key Measurements - Temp:{temperature:F2}°C, Press:{pressure:F3}{(isRMGDevice ? "kPa" : "MPa")}, SOS:{measuredSOS:F4}m/s");
                if (!isRMGDevice)
                {
                    Log.Info($"Key Measurements - Internal Diameter:{intDiam:F3}mm");
                }
            }
            catch (Exception ex)
            {
                Log.Error($"Error logging key measurements: {ex.Message}", ex);
            }
        }

        public AutomaticCheckService(string connectionString, string reportFolderPath, string stationTableName)
        {
            if (string.IsNullOrEmpty(connectionString))
                throw new ArgumentNullException(nameof(connectionString));
            if (string.IsNullOrEmpty(reportFolderPath))
                throw new ArgumentNullException(nameof(reportFolderPath));
            if (string.IsNullOrEmpty(stationTableName))
                throw new ArgumentNullException(nameof(stationTableName));

            _connectionString = connectionString;
            _stationTableName = stationTableName;
            
            // 初始化XML配置读取器
            _xmlConfigReader = new XmlConfigReader("Config/AppConfig.xml");
            
            // 读取自动核查配置
            var configReader = new AutoCheckConfigReader();
            _config = configReader.GetAutoCheckConfig();
            
            // 使用AppConfig.xml中的ReportPath作为根目录，而不是AutoCheckConfig中的目录
            _reportFolderPath = _xmlConfigReader.GetReportPath();
            _fileNamePrefix = _config.FileNamePrefix;
            
            _stationManager = new StationDataManager(connectionString, stationTableName);
            _configManager = new StationConfigManager();
            _checkListDataService = new CheckListDataService(connectionString);
            
            // 只有在配置启用时才初始化定时器
            if (_config.IsEnabled)
            {
                InitializeTimer();
                Log.Info($"AutomaticCheckService initialized successfully - Scheduled for {_config.CheckHour:D2}:{_config.CheckMinute:D2} daily");
                Log.Info($"Report base path set to: {_reportFolderPath}");
            }
            else
            {
                Log.Info("AutomaticCheckService initialized but disabled by configuration");
            }
        }

        /// <summary>
        /// 初始化定时器
        /// </summary>
        private void InitializeTimer()
        {
            // 计算到下一次执行时间的间隔
            var now = DateTime.Now;
            var nextRun = new DateTime(now.Year, now.Month, now.Day, _config.CheckHour, _config.CheckMinute, 0);
            
            // 如果今天的执行时间已过，设置为明天
            if (nextRun <= now)
            {
                nextRun = nextRun.AddDays(1);
            }
            
            var timeToNext = nextRun - now;
            
            // 创建定时器，首次执行后每24小时执行一次
            _dailyTimer = new Timer();
            _dailyTimer.Interval = timeToNext.TotalMilliseconds;
            _dailyTimer.Elapsed += async (sender, e) => 
            {
                // 首次执行后，改为每24小时执行一次
                _dailyTimer.Interval = 24 * 60 * 60 * 1000; // 24小时
                await ExecuteAutomaticCheckAsync();
            };
            
            Log.Info($"Automatic check scheduled for: {nextRun:yyyy-MM-dd HH:mm:ss}");
        }

        /// <summary>
        /// 启动自动核查服务
        /// </summary>
        public void Start()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(AutomaticCheckService));
                
            if (!_config.IsEnabled)
            {
                Log.Info("AutomaticCheckService start requested but disabled by configuration");
                return;
            }
                
            _dailyTimer?.Start();
            Log.Info("AutomaticCheckService started");
        }

        /// <summary>
        /// 停止自动核查服务
        /// </summary>
        public void Stop()
        {
            _dailyTimer?.Stop();
            Log.Info("AutomaticCheckService stopped");
        }

        /// <summary>
        /// 执行自动核查主流程
        /// </summary>
        private async Task ExecuteAutomaticCheckAsync()
        {
            await ExecuteAutomaticCheckAsync(DateTime.Now);
        }

        /// <summary>
        /// 执行自动核查主流程（指定时间）
        /// </summary>
        /// <param name="checkTime">指定的核查时间</param>
        private async Task ExecuteAutomaticCheckAsync(DateTime checkTime)
        {
            if (_isRunning)
            {
                Log.Warn("Automatic check is already running, skipping this execution");
                return;
            }

            _isRunning = true;
            
            try
            {
                Log.Info($"Starting automatic check at {checkTime:yyyy-MM-dd HH:mm:ss}");
                
                // 获取所有需要检查的设备组合
                var deviceCombinations = await GetAllDeviceCombinationsAsync();

                #region For Test Only

                DeviceCombination[] arrayTestDevice = new DeviceCombination[]
                {
                    new DeviceCombination
                    {
                        Station = "可靠性项目",
                        FlowMeter = "FT_11692",
                        FlowComputer = "FC_11692"
                    }
                };

                #endregion

                Log.Info($"Found {deviceCombinations.Count} device combinations to check");
                
                int successCount = 0;
                int failCount = 0;
                
                // 依次对每个设备组合执行核查
                foreach (var device in deviceCombinations)
                {
                    try
                    {
                        Log.Info($"Checking device: Station={device.Station}, FlowMeter={device.FlowMeter}, FlowComputer={device.FlowComputer}");
                        Console.WriteLine($"Checking device: Station={device.Station}, FlowMeter={device.FlowMeter}, FlowComputer={device.FlowComputer}");

                        var success = await ExecuteDeviceCheckAsync(device, checkTime);

                        // for test only
                        //var success = await ExecuteDeviceCheckAsync(arrayTestDevice[0], checkTime);

                        if (success)
                        {
                            successCount++;
                            Log.Info($"Device check completed successfully: {device.FlowMeter}");
                        }
                        else
                        {
                            failCount++;
                            Log.Warn($"Device check failed: {device.FlowMeter}");
                        }
                        
                        // 添加短暂延迟，避免数据库压力过大
                        await Task.Delay(1000);
                    }
                    catch (Exception ex)
                    {
                        failCount++;
                        Log.Error($"Error checking device {device.FlowMeter}: {ex.Message}", ex);
                    }
                }
                
                Log.Info($"Automatic check completed. Success: {successCount}, Failed: {failCount}");
            }
            catch (Exception ex)
            {
                Log.Error($"Error in automatic check execution: {ex.Message}", ex);
            }
            finally
            {
                _isRunning = false;
            }
        }

        /// <summary>
        /// 获取所有需要检查的设备组合
        /// </summary>
        private async Task<List<DeviceCombination>> GetAllDeviceCombinationsAsync()
        {
            var combinations = new List<DeviceCombination>();
            
            try
            {
                // 从数据库获取所有不重复的站点、流量计、流量计算机组合
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    
                    var sql = $@"
                        SELECT
                            MIN(Station) AS Station, -- 选择每个FlowMeterTag对应的最小Station值
                            FlowMeterTag,
                            MIN(FlowComputerTag) AS FlowComputerTag -- 选择每个FlowMeterTag对应的最小FlowComputerTag值
                        FROM {_stationTableName}
                        WHERE Station IS NOT NULL
                            AND FlowMeterTag IS NOT NULL
                            AND FlowComputerTag IS NOT NULL
                        GROUP BY FlowMeterTag
                        ORDER BY FlowMeterTag;";
                    
                    using (var command = new SqlCommand(sql, connection))
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            combinations.Add(new DeviceCombination
                            {
                                Station = reader.GetString(0),  // Station
                                FlowMeter = reader.GetString(1),  // FlowMeterTag
                                FlowComputer = reader.GetString(2)  // FlowComputerTag
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error($"Error getting device combinations: {ex.Message}", ex);
                throw;
            }
            
            return combinations;
        }

        /// <summary>
        /// 执行单个设备的核查
        /// </summary>
        private async Task<bool> ExecuteDeviceCheckAsync(DeviceCombination device, DateTime checkTime)
        {
            try
            {
                // 1. 获取设备厂商信息并判断是否为RMG设备
                var deviceManufacturer = await GetDeviceManufacturerAsync(device.FlowMeter);
                var isRMGDevice = deviceManufacturer.Equals("RMG", StringComparison.OrdinalIgnoreCase);
                
                // 初始化字段映射
                InitializeFieldMappings(isRMGDevice);
                
                // 2. 获取设备序列号
                var currentDeviceSN = await GetFlowMeterSerialNumberAsync(device.FlowComputer, device.FlowMeter);
                Log.Info($"Device SN: {currentDeviceSN ?? "NOT Found!"} - FC: {device.FlowComputer}, FT: {device.FlowMeter}");
                
                // 3. 获取上次检查时间
                var deviceLastCheckTime = await GetLatestCheckTimeAsync(device.FlowComputer, device.FlowMeter);
                if (deviceLastCheckTime.HasValue)
                {
                    Log.Info($"Last check time: {deviceLastCheckTime.Value:yyyy-MM-dd HH:mm:ss} - FC: {device.FlowComputer}, FT: {device.FlowMeter}");
                }
                else
                {
                    Log.Info($"This is the FIRST time checking the device - FC: {device.FlowComputer}, FT: {device.FlowMeter}");
                }
                
                // 4. 获取FC表数据（result字典）
                var fcData = await GetFCTableDataAsync(device.FlowComputer, checkTime);
                if (fcData == null || fcData.Count == 0)
                {
                    Log.Warn($"No FC data found for device: {device.FlowComputer}");
                    return false;
                }
                
                // 5. 获取FT表数据（resultFT字典）
                var ftData = await GetFTTableDataAsync(device.FlowMeter, checkTime);
                if (ftData == null || ftData.Count == 0)
                {
                    Log.Warn($"No FT data found for device: {device.FlowMeter}");
                    return false;
                }
                
                // 6. 获取报告数据（_reportData列表）
                var reportData = await GetReportDataAsync(device.FlowMeter, checkTime, 10);
                if (reportData == null || reportData.Count == 0)
                {
                    Log.Warn($"No report data found for device: {device.FlowMeter}");
                    return false;
                }
                
                // 7. 数据验证和日志记录
                LogKeyMeasurements(fcData, ftData, isRMGDevice);
                var totalComposition = CalculateTotalComposition(fcData);
                var systemStatusGood = VerifySystemStatus(ftData, isRMGDevice);
                
                // 8. 执行AGA10计算
                var calculationResult = ExecuteAGA10Calculation(fcData, device.FlowMeter, isRMGDevice);
                if (calculationResult == null)
                {
                    Log.Warn($"AGA10 calculation failed for device: {device.FlowMeter}");
                    return false;
                }
                
                // 9. 生成Excel报表（传入所有必要数据）
                var reportSuccess = await GenerateExcelReportAsync(device, fcData, ftData, reportData, calculationResult, checkTime, isRMGDevice, currentDeviceSN, deviceLastCheckTime);
                if (!reportSuccess)
                {
                    Log.Warn($"Excel report generation failed for device: {device.FlowMeter}");
                    return false;
                }
                
                // 10. 插入检查记录到数据库
                var recordSuccess = await _checkListDataService.InsertCheckListRecordAsync(
                    device.FlowComputer,
                    device.FlowMeter,
                    calculationResult.CalculatedSOS.ToString("F4"),
                    calculationResult.MeasuredSOS.ToString("F4"),
                    calculationResult.Deviation.ToString("F4"),
                    checkTime
                );
                
                if (!recordSuccess)
                {
                    Log.Warn($"Check record insertion failed for device: {device.FlowMeter}");
                }
                
                // 11. 完成检查的总结日志
                Log.Info($"Device check completed successfully - FC: {device.FlowComputer}, FT: {device.FlowMeter}");
                Log.Info($"Summary - TotalComp: {totalComposition:F2}%, SystemStatus: {systemStatusGood}, SOS Deviation: {calculationResult.Deviation:F4}m/s");
                
                return true;
            }
            catch (Exception ex)
            {
                Log.Error($"Error in device check for {device.FlowMeter}: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取FC表数据（对应Form1中的result字典）
        /// </summary>
        private async Task<Dictionary<string, string>> GetFCTableDataAsync(string flowComputerTag, DateTime checkTime)
        {
            try
            {
                Log.Info($"Getting FC table data for: {flowComputerTag} at {checkTime:yyyy-MM-dd HH:mm:ss}");
                
                var result = new Dictionary<string, string>();
                
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    
                    var sqlFC = @"
                        DECLARE @InputDateTime DATETIME = CONVERT(DATETIME, @TargetTime, 101);

                        SELECT TOP 1 *
                        FROM {0}
                        WHERE LocalTimeCol <= @InputDateTime
                        ORDER BY LocalTimeCol DESC";
                    
                    // 替换表名
                    sqlFC = string.Format(sqlFC, flowComputerTag);
                    
                    using (var command = new SqlCommand(sqlFC, connection))
                    {
                        command.Parameters.AddWithValue("@TargetTime", checkTime);
                        
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                for (var i = 0; i < reader.FieldCount; i++)
                                {
                                    result[reader.GetName(i)] = reader[i].ToString();
                                }
                                
                                Log.Info($"Successfully retrieved FC data: {result.Count} fields from {flowComputerTag}");
                                return result;
                            }
                            else
                            {
                                Log.Warn($"No FC data found for {flowComputerTag} at {checkTime}");
                                return null;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error($"Error getting FC table data for {flowComputerTag}: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 获取FT表数据（对应Form1中的resultFT字典）
        /// </summary>
        private async Task<Dictionary<string, string>> GetFTTableDataAsync(string flowMeterTag, DateTime checkTime)
        {
            try
            {
                Log.Info($"Getting FT table data for: {flowMeterTag} at {checkTime:yyyy-MM-dd HH:mm:ss}");
                
                var resultFT = new Dictionary<string, string>();
                
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    
                    var sqlFT = @"
                        DECLARE @InputDateTime DATETIME = CONVERT(DATETIME, @TargetTime, 101);

                        SELECT TOP 1 *
                        FROM {0}
                        WHERE LocalTimeCol <= @InputDateTime
                        ORDER BY LocalTimeCol DESC";
                    
                    // 替换表名
                    sqlFT = string.Format(sqlFT, flowMeterTag);
                    
                    using (var command = new SqlCommand(sqlFT, connection))
                    {
                        command.Parameters.AddWithValue("@TargetTime", checkTime);
                        
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                for (var i = 0; i < reader.FieldCount; i++)
                                {
                                    resultFT[reader.GetName(i)] = reader[i].ToString();
                                }
                                
                                Log.Info($"Successfully retrieved FT data: {resultFT.Count} fields from {flowMeterTag}");
                                return resultFT;
                            }
                            else
                            {
                                Log.Warn($"No FT data found for {flowMeterTag} at {checkTime}");
                                return null;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error($"Error getting FT table data for {flowMeterTag}: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 获取报告数据（对应Form1中的_reportData列表）
        /// 这是一个简化版本的ReadDataInTimeRangeAsync方法
        /// </summary>
        private async Task<List<Dictionary<string, string>>> GetReportDataAsync(string flowMeterTag, DateTime targetTime, int durationMinutes)
        {
            try
            {
                Log.Info($"Getting report data for: {flowMeterTag} from {targetTime.AddMinutes(-durationMinutes):yyyy-MM-dd HH:mm:ss} to {targetTime:yyyy-MM-dd HH:mm:ss}");
                
                var reportData = new List<Dictionary<string, string>>();
                
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    
                    var sql = @"
                        SELECT *
                        FROM {0}
                        WHERE LocalTimeCol >= @StartTime AND LocalTimeCol <= @EndTime
                        ORDER BY LocalTimeCol ASC";
                    
                    // 替换表名
                    sql = string.Format(sql, flowMeterTag);
                    
                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@StartTime", targetTime.AddMinutes(-durationMinutes));
                        command.Parameters.AddWithValue("@EndTime", targetTime);
                        
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var row = new Dictionary<string, string>();
                                for (var i = 0; i < reader.FieldCount; i++)
                                {
                                    row[reader.GetName(i)] = reader[i].ToString();
                                }
                                reportData.Add(row);
                            }
                        }
                    }
                }
                
                Log.Info($"Successfully retrieved report data: {reportData.Count} records from {flowMeterTag}");
                return reportData;
            }
            catch (Exception ex)
            {
                Log.Error($"Error getting report data for {flowMeterTag}: {ex.Message}", ex);
                throw;
            }
        }



        /// <summary>
        /// 获取设备厂商信息
        /// </summary>
        private async Task<string> GetDeviceManufacturerAsync(string flowMeterTag)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    
                    var sql = $@"
                        SELECT DeviceManufacturer 
                        FROM {_stationTableName} 
                        WHERE FlowMeterTag = @FlowMeterTag";
                    
                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@FlowMeterTag", flowMeterTag);
                        
                        var result = await command.ExecuteScalarAsync();
                        return result?.ToString() ?? "";
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error($"Error getting device manufacturer for {flowMeterTag}: {ex.Message}", ex);
                return "";
            }
        }

        /// <summary>
        /// 获取设备序列号
        /// </summary>
        private async Task<string> GetFlowMeterSerialNumberAsync(string flowComputerTag, string flowMeterTag)
        {
            try
            {
                Log.Info($"GetFlowMeterSerialNumber - FC: {flowComputerTag}, FT: {flowMeterTag}");

                var result = await _checkListDataService.GetFlowMeterSerialNumberAsync(
                    flowComputerTag,
                    flowMeterTag
                );

                if (!string.IsNullOrEmpty(result))
                {
                    Log.Info(
                        $"GetFlowMeterSerialNumberAsync Successful: {result} - FC: {flowComputerTag}, FT: {flowMeterTag}"
                    );
                }
                else
                {
                    Log.Info(
                        $"GetFlowMeterSerialNumberAsync FAILED - FC: {flowComputerTag}, FT: {flowMeterTag}"
                    );
                }

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(
                    $"ERROR when call GetFlowMeterSerialNumber - FC: {flowComputerTag}, FT: {flowMeterTag}",
                    ex
                );

                return null;
            }
        }

        /// <summary>
        /// 获取上次检查时间
        /// </summary>
        private async Task<DateTime?> GetLatestCheckTimeAsync(string flowComputerTag, string flowMeterTag)
        {
            try
            {
                Log.Info($"GetLatestCheckTime - FC: {flowComputerTag}, FT: {flowMeterTag}");

                var result = await _checkListDataService.GetLatestCheckTimeAsync(
                    flowComputerTag,
                    flowMeterTag
                );

                if (result.HasValue)
                {
                    Log.Info(
                        $"Last check time: {result.Value:yyyy-MM-dd HH:mm:ss} - FC: {flowComputerTag}, FT: {flowMeterTag}"
                    );
                }
                else
                {
                    Log.Info(
                        $"Last check time NOT FOUND - FC: {flowComputerTag}, FT: {flowMeterTag}"
                    );
                }

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(
                    $"GetLatestCheckTime ERROR - FC: {flowComputerTag}, FT: {flowMeterTag}",
                    ex
                );
                return null;
            }
        }

                /// <summary>
        /// 执行AGA10计算 - 修复版本：移除Task.Run，添加线程安全保护，防止FatalExecutionEngineError
        /// </summary>
        private CalculationResult ExecuteAGA10Calculation(Dictionary<string, string> data, string flowMeterTag, bool isRMGDevice)
        {
            // 使用线程安全锁保护AGA10 DLL调用
            lock (_aga10Lock)
            {
                // 预定义字符串常量，避免动态字符串连接导致的堆栈问题
                const string HISTORY_TAG_SUFFIX = "_1";
                
                // 预定义所有字段名，避免运行时字符串操作
                const string METHANE_FIELD = "SLCT_METHANE_1";
                const string ETHANE_FIELD = "SLCT_ETHANE_1";
                const string PROPANE_FIELD = "SLCT_PROPANE_1";
                const string N_BUTANE_FIELD = "SLCT_N_BUTANE_1";
                const string I_BUTANE_FIELD = "SLCT_I_BUTANE_1";
                const string N_PENTANE_FIELD = "SLCT_N_PENTANE_1";
                const string I_PENTANE_FIELD = "SLCT_I_PENTANE_1";
                const string NEO_PENTANE_FIELD = "SLCT_NEO_PENTANE_1";
                const string HEXANE_FIELD = "SLCT_HEXANE_1";
                const string NITROGEN_FIELD = "SLCT_NITROGEN_1";
                const string CO2_FIELD = "SLCT_CO2_1";
                const string PRESSURE_FIELD = "PressInuse_1";
                const string TEMPERATURE_FIELD = "TempInuse_1";
                
                // 根据设备类型选择正确的声速字段
                string USM_SOS_FIELD = isRMGDevice ? "USMAvgVOSInuse_1" : "USMAvgVOS_1";
                
                try
                {
                    Log.Info($"AGA10 Calculation start - Device: {flowMeterTag}, isRMG: {isRMGDevice}");
                    
                    // ===== 第一步：提前获取测量声速，避免在DLL调用后进行字符串操作 =====
                    double measuredSOS = 0.0;
                    try
                    {
                        if (data != null && data.TryGetValue(USM_SOS_FIELD, out string sosValueStr) && 
                            !string.IsNullOrEmpty(sosValueStr) && double.TryParse(sosValueStr, out measuredSOS))
                        {
                            Log.Info($"Get the measuredSOS: {measuredSOS:F4} m/s");
                        }
                        else
                        {
                            Log.Error($"Cannot get the measuredSOS: {USM_SOS_FIELD}");
                            return null;
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error($"Cannot get the measuredSOS: {ex.Message}", ex);
                        return null;
                    }
                    
                    if (measuredSOS <= 0)
                    {
                        Log.Error($"The measuredSOS <= 0: {measuredSOS}");
                        return null;
                    }
                    
                    // ===== 第二步：安全解析其他数据 =====
                    // 预先解析所有数据，避免在DLL调用过程中进行字符串操作
                    double methane = 0, ethane = 0, propane = 0, nButane = 0, iButane = 0;
                    double nPentane = 0, iPentane = 0, neoPentane = 0, hexane = 0, nitrogen = 0, co2 = 0;
                    double pressure = 0, temperature = 0;
                    
                    try
                    {
                        // 安全解析气体组分
                        data.TryGetValue(METHANE_FIELD, out string methaneStr);
                        data.TryGetValue(ETHANE_FIELD, out string ethaneStr);
                        data.TryGetValue(PROPANE_FIELD, out string propaneStr);
                        data.TryGetValue(N_BUTANE_FIELD, out string nButaneStr);
                        data.TryGetValue(I_BUTANE_FIELD, out string iButaneStr);
                        data.TryGetValue(N_PENTANE_FIELD, out string nPentaneStr);
                        data.TryGetValue(I_PENTANE_FIELD, out string iPentaneStr);
                        data.TryGetValue(NEO_PENTANE_FIELD, out string neoPentaneStr);
                        data.TryGetValue(HEXANE_FIELD, out string hexaneStr);
                        data.TryGetValue(NITROGEN_FIELD, out string nitrogenStr);
                        data.TryGetValue(CO2_FIELD, out string co2Str);
                        data.TryGetValue(PRESSURE_FIELD, out string pressureStr);
                        data.TryGetValue(TEMPERATURE_FIELD, out string temperatureStr);
                        
                        // 转换为数值
                        double.TryParse(methaneStr, out methane);
                        double.TryParse(ethaneStr, out ethane);
                        double.TryParse(propaneStr, out propane);
                        double.TryParse(nButaneStr, out nButane);
                        double.TryParse(iButaneStr, out iButane);
                        double.TryParse(nPentaneStr, out nPentane);
                        double.TryParse(iPentaneStr, out iPentane);
                        double.TryParse(neoPentaneStr, out neoPentane);
                        double.TryParse(hexaneStr, out hexane);
                        double.TryParse(nitrogenStr, out nitrogen);
                        double.TryParse(co2Str, out co2);
                        double.TryParse(pressureStr, out pressure);
                        double.TryParse(temperatureStr, out temperature);
                        
                        Log.Info($"AGA10 input data - CH4:{methane:F2}%, N2:{nitrogen:F2}%, CO2:{co2:F2}%");
                        Log.Info($"AGA10 input data - Temp:{temperature:F2}°C, Press:{pressure:F2}kPa");
                    }
                    catch (Exception ex)
                    {
                        Log.Error($"AGA10 input data ERROR: {ex.Message}", ex);
                        return null;
                    }
                    
                    // ===== 第三步：创建和初始化AGA10结构体 =====
                    var aga10Struct = new AGA10STRUCT();
                    long initResult = 0;
                    try
                    {
                        initResult = AGA10_Init();
                        Log.Info($"AGA10_Init result code: {initResult}");
                    }
                    catch (Exception ex)
                    {
                        Log.Error($"AGA10_Init ERROR: {ex.Message}", ex);
                        return null;
                    }
                    if (initResult != 9001)
                    {
                        Log.Error($"AGA10_Init FAILED，expect 9001，actual: {initResult}");
                        return null;
                    }

                    aga10Struct.lStatus = 9000;
                    //aga10Struct.bForceUpdate = 0;

                    // 设置气体组分（转换为摩尔分数）
                    aga10Struct.Methane = methane / 100.0;
                    aga10Struct.Ethane = ethane / 100.0;
                    aga10Struct.Propane = propane / 100.0;
                    aga10Struct.n_Butane = nButane / 100.0;
                    aga10Struct.i_Butane = iButane / 100.0;
                    aga10Struct.n_Pentane = nPentane / 100.0;
                    aga10Struct.i_Pentane = (iPentane + neoPentane) / 100.0;
                    aga10Struct.n_Hexane = hexane / 100.0;
                    aga10Struct.Nitrogen = nitrogen / 100.0;
                    aga10Struct.CO2 = co2 / 100.0;
                    
                    // 显式设置其他组分为0
                    aga10Struct.H2O = 0.0;
                    aga10Struct.H2S = 0.0;
                    aga10Struct.H2 = 0.0;
                    aga10Struct.CO = 0.0;
                    aga10Struct.O2 = 0.0;
                    aga10Struct.n_Heptane = 0.0;
                    aga10Struct.n_Octane = 0.0;
                    aga10Struct.n_Nonane = 0.0;
                    aga10Struct.n_Decane = 0.0;
                    aga10Struct.He = 0.0;
                    aga10Struct.Ar = 0.0;
                    
                    // 设置温度和压力
                    aga10Struct.dPb = 101.325 * 1000.0; // Pa
                    aga10Struct.dTb = 20.0 + 273.15;   // K
                    if (isRMGDevice)
                    {
                        aga10Struct.dPf = pressure * 1000.0; // kpa to Pa
                    }
                    else
                    {
                        aga10Struct.dPf = pressure * 1000000.0; // Mpa to Pa

                    }
                    aga10Struct.dTf = temperature + 273.15; // K
                    
                    // 验证关键数据
                    if (aga10Struct.dPf <= 0 || aga10Struct.dTf <= 273.15)
                    {
                        Log.Error($"Data ERROR - Temp: {aga10Struct.dTf - 273.15:F2}°C, Press: {aga10Struct.dPf / 1000:F2}kPa");
                        return null;
                    }
                    
                    // ===== 第四步：执行AGA10计算 =====
                    
                    double calculatedSOS = 0.0;
                    
                    
                    try
                    {
                        double db = 0.0;
                        double critResult = 0.0;
                      

                        try
                        {
                            critResult = Crit(ref aga10Struct, db);
                            calculatedSOS = aga10Struct.dSOS; // 立即保存计算结果
                            Log.Info($"Crit completed，Result: {critResult}, calculatedSOS: {calculatedSOS:F4} m/s");
                        }
                        catch (Exception ex)
                        {
                            Log.Error($"Crit ERROR: {ex.Message}", ex);
                            return null;
                        }
                        
                        // 验证计算结果
                        if (double.IsNaN(calculatedSOS) || double.IsInfinity(calculatedSOS) || calculatedSOS <= 0)
                        {
                            Log.Error($"AGA10 ERROR: {calculatedSOS}");
                            return null;
                        }
                    }
                    finally
                    {
                        // 确保AGA10_UnInit始终被调用
                        try
                        {
                            long uninitResult = AGA10_UnInit();
                            Log.Info($"AGA10_UnInit result code: {uninitResult}");
                            if (uninitResult != 0)
                            {
                                Log.Warn($"AGA10_UnInit result code ERROR: {uninitResult}");
                            }
                        }
                        catch (Exception ex)
                        {
                            Log.Error($"AGA10_UnInit ERROR: {ex.Message}", ex);
                        }
                    }
                    
                    // ===== 第五步：计算偏差并返回结果 =====
                    // 在DLL调用完成后，进行简单的数值计算
                    double deviation;
                    if (Math.Abs(calculatedSOS) < 1e-10) // 处理除数为0的情况，使用极小值进行比较
                    {
                        deviation = 0.0; // 或者可以设置为其他默认值，如double.NaN
                        Log.Error($"CalculatedSOS is zero or near zero ({calculatedSOS}), setting deviation to 0");
                    }
                    else
                    {
                        deviation = Math.Round((measuredSOS - calculatedSOS) / calculatedSOS * 100, 5); // 保留5位小数
                    }
                    
                    Log.Info($"SOS deviation - Measured: {measuredSOS:F4} m/s, Calculated: {calculatedSOS:F4} m/s, Dev: {deviation:F5}%");
                    
                    // 创建结果对象 - 使用简单的对象初始化
                    var result = new CalculationResult();
                    result.CalculatedSOS = calculatedSOS;
                    result.MeasuredSOS = measuredSOS;
                    result.Deviation = deviation;
                    result.IsValid = true;
                    
                    Log.Info($"AGA10 completed - Device: {flowMeterTag}");
                    return result;
                }
                catch (AccessViolationException ex)
                {
                    Log.Error($"AGA10 DLL AccessViolationException - {flowMeterTag}: {ex.Message}", ex);
                    return null;
                }
                catch (Exception ex)
                {
                    Log.Error($"AGA10 ERROR - {flowMeterTag}: {ex.Message}", ex);
                    return null;
                }
            }
        }
        
        /// <summary>
        /// 验证AGA10计算所需的必要数据字段
        /// </summary>
        private bool ValidateRequiredDataFields(Dictionary<string, string> data)
        {
            var requiredFields = new[]
            {
                "SLCT_METHANE_1", "SLCT_ETHANE_1", "SLCT_PROPANE_1", "SLCT_N_BUTANE_1", 
                "SLCT_I_BUTANE_1", "SLCT_N_PENTANE_1", "SLCT_I_PENTANE_1", "SLCT_NEO_PENTANE_1", 
                "SLCT_HEXANE_1", "SLCT_NITROGEN_1", "SLCT_CO2_1", 
                "PressInuse_1", "TempInuse_1", "USMAvgVOS_1"
            };
            
            var missingFields = new List<string>();
            
            foreach (var field in requiredFields)
            {
                if (!data.ContainsKey(field))
                {
                    missingFields.Add(field);
                }
            }
            
            if (missingFields.Count > 0)
            {
                Log.Error($"缺少必要字段: {string.Join(", ", missingFields)}");
                return false;
            }
            
            Log.Info($"所有必要字段验证通过，共验证 {requiredFields.Length} 个字段");
            return true;
        }

        /// <summary>
        /// 生成Excel报表
        /// </summary>
        private async Task<bool> GenerateExcelReportAsync(DeviceCombination device, Dictionary<string, string> fcData, Dictionary<string, string> ftData, List<Dictionary<string, string>> reportData, 
            CalculationResult calculation, DateTime checkTime, bool isRMGDevice, string deviceSN, DateTime? lastCheckTime)
        {
            return await Task.Run(async () =>
            {
                try
                {
                    // 设置EPPlus许可证
                    ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                    
                    // 选择模板
                    var templatePath = isRMGDevice ? 
                        Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config", "SOSCheckReportTemplate_RMG.xlsx") :
                        Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config", "SOSCheckReportTemplate.xlsx");
                    
                    if (!File.Exists(templatePath))
                    {
                        Log.Error($"Excel template not found: {templatePath}");
                        return false;
                    }
                    
                    // 构建报告保存路径：根目录 + station名 + ft设备名 + SOSCheckReport
                    var reportDirectory = Path.Combine(_reportFolderPath, device.Station.Trim(), device.FlowMeter.Trim(), "SOSCheckReport");
                    
                    // 生成报表文件名
                    var fileName = $"{_fileNamePrefix}_{device.FlowMeter}_{checkTime:yyyy-MM-dd HH-mm-ss}.xlsx";
                    var filePath = Path.Combine(reportDirectory, fileName);
                    
                    // 确保目录存在
                    Directory.CreateDirectory(reportDirectory);
                    
                    Log.Info($"Report will be saved to: {reportDirectory}");
                    Log.Info($"Full file path: {filePath}");
                    
                    // 复制模板文件
                    File.Copy(templatePath, filePath, true);
                    
                    // 填充数据到Excel
                    using (var package = new ExcelPackage(new FileInfo(filePath)))
                    {
                        var worksheetCount = package.Workbook.Worksheets.Count;
                        Log.Info($"Excel template has {worksheetCount} worksheets");
                        
                        // 获取不同的工作表
                        var sheet0 = package.Workbook.Worksheets[0];  // 基本信息
                        var sheet2 = package.Workbook.Worksheets[2];  // 计算结果
                        var sheet3 = package.Workbook.Worksheets[3];  // 数据详情
                        
                        Log.Info($"Using worksheets - Basic Info: {sheet0.Name}, Calculation: {sheet2.Name}, Data Details: {sheet3.Name}");
                        
                        // 填充基本信息到Sheet0
                        FillExcelBasicInfo(sheet0, device, fcData,ftData,checkTime, deviceSN, lastCheckTime, isRMGDevice);
                        Log.Info("Basic info filled successfully");
                        
                        // 填充计算结果到Sheet2
                        FillExcelCalculationResults(sheet2,fcData, calculation, isRMGDevice);
                        Log.Info("Calculation results filled successfully");
                        
                        // 填充数据详情到Sheet3
                        FillExcelDataDetails(sheet3, fcData, ftData, reportData, device.FlowMeter, isRMGDevice);
                        Log.Info("Data details filled successfully");
                        
                        // 保护工作表
                        for (var j = 0; j < 4; j++)
                        {
                            package.Workbook.Worksheets[j].ProtectedRanges.Add(
                                "protected",
                                new ExcelAddress(1, 1, 150, 150)
                            );
                            package.Workbook.Worksheets[j].Protection.SetPassword("Admin123");
                            package.Workbook.Worksheets[j].Protection.IsProtected = true;
                            package.Workbook.Worksheets[j].Protection.AllowEditObject = false;
                            package.Workbook.Worksheets[j].Protection.AllowEditScenarios = false;
                            package.Workbook.Worksheets[j].Protection.AllowSelectUnlockedCells = false;
                        }
                        Log.Info("Excel worksheets protected successfully");
                        
                        // 保存文件
                        await package.SaveAsync();
                        Log.Info($"Excel file saved successfully: {fileName}");
                    }
                    
                    Log.Info($"Excel report generated successfully: {fileName}");
                    return true;
                }
                catch (Exception ex)
                {
                    Log.Error($"Error generating Excel report for {device.FlowMeter}: {ex.Message}", ex);
                    return false;
                }
            });
        }

        /// <summary>
        /// 填充Excel-InsecptionReport
        /// </summary>
        private void FillExcelBasicInfo(OfficeOpenXml.ExcelWorksheet worksheet, DeviceCombination device, Dictionary<string, string> fcData, Dictionary<string, string> ftData, DateTime checkTime, string deviceSN, DateTime? lastCheckTime, bool isRMGDevice)
        {
            // 填充状态信息
            FillDeviceStatusInfo(worksheet, ftData, isRMGDevice);

            if (isRMGDevice)
            {


                // RMG设备特有数据
                var gasVel = ParseDoubleValue(fcData, "USMAvgVOSInuse" + historyTagSuffix);
                worksheet.Cells["F12"].Value = gasVel;  // 气体流速

                Log.Info($"RMG Device Data - Gas Velocity: {gasVel:F4}m/s");
            }
            else
            {
               
                // DANIEL设备特有数据
                var gasVel = ParseDoubleValue(fcData, "USMAvgVOS" + historyTagSuffix);
                var intDiam = ParseDoubleValue(fcData, "SpoolIntDia" + historyTagSuffix);

                var fwdFlwRt1 = ParseDoubleValue(ftData, "FwdFlwRt1" );
                var fwdFlwRt2 = ParseDoubleValue(ftData, "FwdFlwRt2" );
                var fwdFlwRt3 = ParseDoubleValue(ftData, "FwdFlwRt3" );
                var fwdFlwRt4 = ParseDoubleValue(ftData, "FwdFlwRt4" );
                var fwdFlwRt5 = ParseDoubleValue(ftData, "FwdFlwRt5" );
                var fwdFlwRt6 = ParseDoubleValue(ftData, "FwdFlwRt6" );
                var fwdFlwRt7 = ParseDoubleValue(ftData, "FwdFlwRt7" );
                var fwdFlwRt8 = ParseDoubleValue(ftData, "FwdFlwRt8" );
                var fwdFlwRt9 = ParseDoubleValue(ftData, "FwdFlwRt9" );
                var fwdFlwRt10 = ParseDoubleValue(ftData, "FwdFlwRt10" );
                var fwdFlwRt11 = ParseDoubleValue(ftData, "FwdFlwRt11" );
                var fwdFlwRt12 = ParseDoubleValue(ftData, "FwdFlwRt12" );

                var fwdMtrFctr1 = ParseDoubleValue(ftData, "FwdMtrFctr1" );
                var fwdMtrFctr2 = ParseDoubleValue(ftData, "FwdMtrFctr2" );
                var fwdMtrFctr3 = ParseDoubleValue(ftData, "FwdMtrFctr3" );
                var fwdMtrFctr4 = ParseDoubleValue(ftData, "FwdMtrFctr4" );
                var fwdMtrFctr5 = ParseDoubleValue(ftData, "FwdMtrFctr5" );
                var fwdMtrFctr6 = ParseDoubleValue(ftData, "FwdMtrFctr6" );
                var fwdMtrFctr7 = ParseDoubleValue(ftData, "FwdMtrFctr7" );
                var fwdMtrFctr8 = ParseDoubleValue(ftData, "FwdMtrFctr8" );
                var fwdMtrFctr9 = ParseDoubleValue(ftData, "FwdMtrFctr9" );
                var fwdMtrFctr10 = ParseDoubleValue(ftData, "FwdMtrFctr10" );
                var fwdMtrFctr11 = ParseDoubleValue(ftData, "FwdMtrFctr11" );
                var fwdMtrFctr12 = ParseDoubleValue(ftData, "FwdMtrFctr12" );

                worksheet.Cells["F12"].Value = gasVel;    // 气体流速
                worksheet.Cells["B8"].Value = intDiam;   // 内径

                worksheet.Cells["I20"].Value = fwdFlwRt1;   
                worksheet.Cells["I21"].Value = fwdFlwRt2;   
                worksheet.Cells["I22"].Value = fwdFlwRt3;   
                worksheet.Cells["I23"].Value = fwdFlwRt4;   
                worksheet.Cells["I24"].Value = fwdFlwRt5;   
                worksheet.Cells["I25"].Value = fwdFlwRt6;   
                worksheet.Cells["I26"].Value = fwdFlwRt7;   
                worksheet.Cells["I27"].Value = fwdFlwRt8;   
                worksheet.Cells["I28"].Value = fwdFlwRt9;   
                worksheet.Cells["I29"].Value = fwdFlwRt10;   
                worksheet.Cells["I30"].Value = fwdFlwRt11;   
                worksheet.Cells["I31"].Value = fwdFlwRt12;

                worksheet.Cells["J20"].Value = fwdMtrFctr1;
                worksheet.Cells["J21"].Value = fwdMtrFctr2;
                worksheet.Cells["J22"].Value = fwdMtrFctr3;
                worksheet.Cells["J23"].Value = fwdMtrFctr4;
                worksheet.Cells["J24"].Value = fwdMtrFctr5;
                worksheet.Cells["J25"].Value = fwdMtrFctr6;
                worksheet.Cells["J26"].Value = fwdMtrFctr7;
                worksheet.Cells["J27"].Value = fwdMtrFctr8;
                worksheet.Cells["J28"].Value = fwdMtrFctr9;
                worksheet.Cells["J29"].Value = fwdMtrFctr10;
                worksheet.Cells["J30"].Value = fwdMtrFctr11;
                worksheet.Cells["J31"].Value = fwdMtrFctr12;

                Log.Info($"DANIEL Device Data - Pressure: {ParseDoubleValue(fcData, "PressInuse" + historyTagSuffix):F3}MPa, Gas Velocity: {gasVel:F4}m/s, Internal Diameter: {intDiam:F3}mm");
            }
            // 根据实际Excel模板调整单元格位置
            worksheet.Cells["B1"].Value = device.Station;           // 站点
            worksheet.Cells["B2"].Value = device.FlowMeter;         // 流量计
            worksheet.Cells["B7"].Value = deviceSN ?? "N/A";       // 设备序列号
            worksheet.Cells["K1"].Value = checkTime.ToString("yyyy-MM-dd");        // 检查日期
            worksheet.Cells["K2"].Value = checkTime.ToString("HH:mm:ss");          // 检查时间
            worksheet.Cells["G2"].Value = lastCheckTime.HasValue ? 
                lastCheckTime.Value.ToString("yyyy-MM-dd HH:mm:ss") : "N/A";       // 上次检查时间
        }

        /// <summary>
        /// 填充Excel-AGA10计算结果
        /// </summary>
        private void FillExcelCalculationResults(OfficeOpenXml.ExcelWorksheet worksheet, Dictionary<string, string> fcData, CalculationResult calculation,bool isRMGDevice)
        {
            // 填充气体组分数据（根据Form1中的实际字段名）
            worksheet.Cells["B5"].Value = ParseDoubleValue(fcData, "SLCT_METHANE" + historyTagSuffix);     // 甲烷
            worksheet.Cells["B6"].Value = ParseDoubleValue(fcData, "SLCT_ETHANE" + historyTagSuffix);     // 乙烷
            worksheet.Cells["B7"].Value = ParseDoubleValue(fcData, "SLCT_PROPANE" + historyTagSuffix);    // 丙烷
            worksheet.Cells["B8"].Value = ParseDoubleValue(fcData, "SLCT_N_BUTANE" + historyTagSuffix);   // 正丁烷
            worksheet.Cells["B9"].Value = ParseDoubleValue(fcData, "SLCT_I_BUTANE" + historyTagSuffix);   // 异丁烷
            worksheet.Cells["B10"].Value = ParseDoubleValue(fcData, "SLCT_N_PENTANE" + historyTagSuffix);  // 正戊烷
            worksheet.Cells["B11"].Value = ParseDoubleValue(fcData, "SLCT_I_PENTANE" + historyTagSuffix);  // 异戊烷
            worksheet.Cells["B12"].Value = ParseDoubleValue(fcData, "SLCT_HEXANE" + historyTagSuffix);     // 己烷
            worksheet.Cells["B13"].Value = ParseDoubleValue(fcData, "SLCT_NITROGEN" + historyTagSuffix);   // 氮气
            worksheet.Cells["B14"].Value = ParseDoubleValue(fcData, "SLCT_CO2" + historyTagSuffix);        // 二氧化碳

            // 填充温度压力数据
            worksheet.Cells["B3"].Value = ParseDoubleValue(fcData, "TempInuse" + historyTagSuffix);       // 温度

            if (isRMGDevice)
            {
                // RMG设备压力单位是kPa，需要转为MPa显示
                var pressureKPa = ParseDoubleValue(fcData, "PressInuse" + historyTagSuffix);
                worksheet.Cells["B2"].Value = Math.Round(pressureKPa / 1000, 3);  // 压力 (MPa)

                Log.Info($"RMG Device Data - Pressure: {pressureKPa:F1}kPa");
            }
            else
            {
                // DANIEL设备压力单位是MPa
                worksheet.Cells["B2"].Value = ParseDoubleValue(fcData, "PressInuse" + historyTagSuffix);  // 压力 (MPa)

                Log.Info($"DANIEL Device Data - Pressure: {ParseDoubleValue(fcData, "PressInuse" + historyTagSuffix):F3}MPa");
            }

            // 根据实际Excel模板调整单元格位置
            worksheet.Cells["B19"].Value = calculation.CalculatedSOS;
            worksheet.Cells["B18"].Value = calculation.MeasuredSOS;
            worksheet.Cells["B20"].Value = calculation.Deviation;
        }

        /// <summary>
        /// 填充Excel-RawData数据详情
        /// </summary>
        private void FillExcelDataDetails(OfficeOpenXml.ExcelWorksheet worksheet, Dictionary<string, string> fcData, Dictionary<string, string> ftData, List<Dictionary<string, string>> reportData, 
            string flowMeterTag, bool isRMGDevice)
        {
            try
            {
                Log.Info($"Filling Excel data details for {flowMeterTag} (RMG: {isRMGDevice})");
                
                
                
                // 填充状态信息
                //FillDeviceStatusInfo(worksheet, ftData, isRMGDevice);
                
                // 填充历史数据统计信息（如果reportData可用）
                if (reportData != null && reportData.Count > 0)
                {
                    FillHistoricalDataSummary(worksheet, reportData, flowMeterTag, isRMGDevice);
                }
                
                Log.Info($"Excel data details filled successfully for {flowMeterTag}");
            }
            catch (Exception ex)
            {
                Log.Error($"Error filling Excel data details for {flowMeterTag}: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 填充设备状态信息到Excel
        /// </summary>
        private void FillDeviceStatusInfo(OfficeOpenXml.ExcelWorksheet worksheet, Dictionary<string, string> ftData, bool isRMGDevice)
        {
            try
            {
                if (isRMGDevice)
                {
                    // RMG设备状态信息
                    var statusP1 = GetMappedFieldValueSimple(ftData, "StatusA"); // → P1Status
                    var statusP2 = GetMappedFieldValueSimple(ftData, "StatusB"); // → P2Status
                    var statusP3 = GetMappedFieldValueSimple(ftData, "StatusC"); // → P3Status
                    var statusP4 = GetMappedFieldValueSimple(ftData, "StatusD"); // → P4Status
                    var statusP5 = GetRMGSpecificFieldValueSimple(ftData, "P5Fault");
                    var statusP6 = GetRMGSpecificFieldValueSimple(ftData, "P6Fault");
                    
                    // 填充状态到Excel（根据模板位置调整）
                    //worksheet.Cells["H10"].Value = statusP1 == 0 ? "正常" : "故障";
                    //worksheet.Cells["H11"].Value = statusP2 == 0 ? "正常" : "故障";
                    //worksheet.Cells["H12"].Value = statusP3 == 0 ? "正常" : "故障";
                    //worksheet.Cells["H13"].Value = statusP4 == 0 ? "正常" : "故障";
                    //worksheet.Cells["H14"].Value = statusP5 == 0 ? "正常" : "故障";
                    //worksheet.Cells["H15"].Value = statusP6 == 0 ? "正常" : "故障";
                    
                    //var isSystemGood = new[] { statusP1, statusP2, statusP3, statusP4, statusP5, statusP6 }.All(s => s == 0);
                    //worksheet.Cells["H16"].Value = isSystemGood ? "正常" : "故障";
                    
                    Log.Info($"RMG Device Status filled - P1:{statusP1}, P2:{statusP2}, P3:{statusP3}, P4:{statusP4}, P5:{statusP5}, P6:{statusP6}");
                }
                else
                {
                    // DANIEL GUSM设备状态信息
                    var statusA = GetMappedFieldValueSimple(ftData, "StatusA");
                    var statusB = GetMappedFieldValueSimple(ftData, "StatusB");
                    var statusC = GetMappedFieldValueSimple(ftData, "StatusC");
                    var statusD = GetMappedFieldValueSimple(ftData, "StatusD");
                    var systemStatus = GetMappedFieldValueSimple(ftData, "SystemStatus");
                    var qMeterValidity = GetMappedFieldValueSimple(ftData, "QMeterValidity");
                    
                    // 填充状态到Excel
                    worksheet.Cells["H10"].Value = statusA == 0 ? "正常" : "故障";
                    worksheet.Cells["H11"].Value = statusB == 0 ? "正常" : "故障";
                    worksheet.Cells["H12"].Value = statusC == 0 ? "正常" : "故障";
                    worksheet.Cells["H13"].Value = statusD == 0 ? "正常" : "故障";
                    worksheet.Cells["H14"].Value = systemStatus == 0 ? "正常" : "故障";
                    worksheet.Cells["B59"].Value = qMeterValidity;

                    var calMethod = GetMappedFieldValueSimple(ftData , "CalMethod");
                    var descCalMethod = calMethod switch
                    {
                        0.0f => "None",
                        1.0f => "Polynomial",
                        2.0f => "Piece-wise linear",
                        _ => "None"
                    };
                    worksheet.Cells["G38"].Value = descCalMethod;

                    Log.Info($"DANIEL Device Status filled - A:{statusA}, B:{statusB}, C:{statusC}, D:{statusD}, System:{systemStatus}");
                }
            }
            catch (Exception ex)
            {
                Log.Error($"Error filling device status info: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 填充历史数据详情 - 逐行插入reportData中的每组数据
        /// </summary>
        private void FillHistoricalDataSummary(OfficeOpenXml.ExcelWorksheet worksheet, List<Dictionary<string, string>> reportData, string flowMeterTag, bool isRMGDevice)
        {
            try
            {
                Log.Info($"Filling historical data details - {reportData.Count} records for {flowMeterTag} (RMG: {isRMGDevice})");
                
                // 构建字段前缀，类似Form1中的streamTablePrefix
                var streamTablePrefix = "";
                
                // 获取系统状态（简化处理，实际应该从ftData获取）
                var isSystemStatusGood = true; // 这里可以传入实际的系统状态
                
                // 逐行插入reportData中的每组数据
                for (var i = 0; i < reportData.Count; i++)
                {
                    var rowNo = i + 2; // 从第2行开始插入数据
                    var record = reportData[i];
                    
                    try
                    {
                        // A列：日期 (对于RMG和非RMG都相同)
                        worksheet.Cells["A" + rowNo].Value = DateTime.Now.ToString("MM/dd/yyyy");
                        
                        // B列：时间戳 (对于RMG和非RMG都相同)
                        worksheet.Cells["B" + rowNo].Value = GetValueOrDefault(record, "LocalTimeCol", "");
                        
                        if (isRMGDevice)
                        {
                            // RMG设备数据插入 (参考Form1的1651-1849行)
                            FillRMGDeviceData(worksheet, record, streamTablePrefix, rowNo, isSystemStatusGood);
                        }
                        else
                        {
                            // 非RMG设备数据插入 (参考Form1的2085-2254行)
                            FillNonRMGDeviceData(worksheet, record, streamTablePrefix, rowNo);
                        }
                        
                    }
                    catch (Exception rowEx)
                    {
                        Log.Error($"Error filling row {rowNo} of historical data: {rowEx.Message}", rowEx);
                        // 继续处理下一行
                    }
                }
                
                Log.Info($"Historical data details filled successfully - {reportData.Count} rows inserted with prefix '{streamTablePrefix}'");
            }
            catch (Exception ex)
            {
                Log.Error($"Error filling historical data details: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 填充RMG设备的历史数据 (A-CQ列)
        /// </summary>
        private void FillRMGDeviceData(OfficeOpenXml.ExcelWorksheet worksheet, Dictionary<string, string> record, string streamTablePrefix, int rowNo, bool isSystemStatusGood)
        {
            // E列：系统状态
            worksheet.Cells["E" + rowNo].Value = isSystemStatusGood;
            
            // F-K列：P1-P6速度数据
            worksheet.Cells["F" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P1Velocity", "0")), 2);
            worksheet.Cells["G" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P2Velocity", "0")), 2);
            worksheet.Cells["H" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P3Velocity", "0")), 2);
            worksheet.Cells["I" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P4Velocity", "0")), 2);
            worksheet.Cells["J" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P5Velocity", "0")), 2);
            worksheet.Cells["K" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P6Velocity", "0")), 2);
            
            // M-R列：P1-P6声速数据
            worksheet.Cells["M" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P1SpeedofSound", "0")), 2);
            worksheet.Cells["N" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P2SpeedofSound", "0")), 2);
            worksheet.Cells["O" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P3SpeedofSound", "0")), 2);
            worksheet.Cells["P" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P4SpeedofSound", "0")), 2);
            worksheet.Cells["Q" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P5SpeedofSound", "0")), 2);
            worksheet.Cells["R" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P6SpeedofSound", "0")), 2);
            
            // T-Y列：P1-P6故障状态
            worksheet.Cells["T" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P1Fault", "0")), 2);
            worksheet.Cells["U" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P2Fault", "0")), 2);
            worksheet.Cells["V" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P3Fault", "0")), 2);
            worksheet.Cells["W" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P4Fault", "0")), 2);
            worksheet.Cells["X" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P5Fault", "0")), 2);
            worksheet.Cells["Y" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P6Fault", "0")), 2);
            
            // Z-AE列：P1-P6有效样本百分比
            worksheet.Cells["Z" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P1ValidSamplePercent", "0")), 2);
            worksheet.Cells["AA" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P2ValidSamplePercent", "0")), 2);
            worksheet.Cells["AB" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P3ValidSamplePercent", "0")), 2);
            worksheet.Cells["AC" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P4ValidSamplePercent", "0")), 2);
            worksheet.Cells["AD" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P5ValidSamplePercent", "0")), 2);
            worksheet.Cells["AE" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P6ValidSamplePercent", "0")), 2);
            
            // AL-AW列：P1-P6 AGC水平 (d1, d2)
            worksheet.Cells["AL" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P1d1AGCLevel", "0")), 2);
            worksheet.Cells["AM" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P1d2AGCLevel", "0")), 2);
            worksheet.Cells["AN" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P2d1AGCLevel", "0")), 2);
            worksheet.Cells["AO" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P2d2AGCLevel", "0")), 2);
            worksheet.Cells["AP" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P3d1AGCLevel", "0")), 2);
            worksheet.Cells["AQ" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P3d2AGCLevel", "0")), 2);
            worksheet.Cells["AR" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P4d1AGCLevel", "0")), 2);
            worksheet.Cells["AS" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P4d2AGCLevel", "0")), 2);
            worksheet.Cells["AT" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P5d1AGCLevel", "0")), 2);
            worksheet.Cells["AU" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P5d2AGCLevel", "0")), 2);
            worksheet.Cells["AV" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P6d1AGCLevel", "0")), 2);
            worksheet.Cells["AW" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P6d2AGCLevel", "0")), 2);
            
            // AX-BI列：P1-P6 SNR (d1, d2)
            worksheet.Cells["AX" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P1d1Snr", "0")), 2);
            worksheet.Cells["AY" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P1d2Snr", "0")), 2);
            worksheet.Cells["AZ" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P2d1Snr", "0")), 2);
            worksheet.Cells["BA" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P2d2Snr", "0")), 2);
            worksheet.Cells["BB" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P3d1Snr", "0")), 2);
            worksheet.Cells["BC" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P3d2Snr", "0")), 2);
            worksheet.Cells["BD" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P4d1Snr", "0")), 2);
            worksheet.Cells["BE" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P4d2Snr", "0")), 2);
            worksheet.Cells["BF" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P5d1Snr", "0")), 2);
            worksheet.Cells["BG" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P5d2Snr", "0")), 2);
            worksheet.Cells["BH" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P6d1Snr", "0")), 2);
            worksheet.Cells["BI" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "P6d2Snr", "0")), 2);
            
            // BR, CB, CK-CQ列：其他参数
            worksheet.Cells["BR" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "ProfileFactor", "0")), 4);
            worksheet.Cells["CB" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "Symmetry", "0")), 4);
            worksheet.Cells["CK" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "TotVolumeD1_L", "0")), 4);
            worksheet.Cells["CL" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "TotVolErrD1_L", "0")), 4);
            worksheet.Cells["CM" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "TotVolumeD2_L", "0")), 4);
            worksheet.Cells["CN" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "TotVolErrD2_L", "0")), 4);
            worksheet.Cells["CO" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "SwirlAnglePlane1", "0")), 4);
            worksheet.Cells["CP" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "SwirlAnglePlane2", "0")), 4);
            worksheet.Cells["CQ" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "SwirlAnglePlane3", "0")), 4);
        }

        /// <summary>
        /// 填充非RMG设备的历史数据
        /// </summary>
        private void FillNonRMGDeviceData(OfficeOpenXml.ExcelWorksheet worksheet, Dictionary<string, string> record, string streamTablePrefix, int rowNo)
        {
            // C-E列：流量数据和系统状态
            worksheet.Cells["C" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "Qmeter", "0")), 2);
            worksheet.Cells["D" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "Qflow", "0")), 2);
            worksheet.Cells["E" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "SystemStatus", "0")), 2);
            
            // F-J列：流速数据
            worksheet.Cells["F" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "FlowVelA", "0")), 2);
            worksheet.Cells["G" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "FlowVelB", "0")), 2);
            worksheet.Cells["H" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "FlowVelC", "0")), 2);
            worksheet.Cells["I" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "FlowVelD", "0")), 2);
            worksheet.Cells["J" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "AvgFlow", "0")), 2);
            
            // K-O列：声速数据
            worksheet.Cells["K" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "SndVelA", "0")), 2);
            worksheet.Cells["L" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "SndVelB", "0")), 2);
            worksheet.Cells["M" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "SndVelC", "0")), 2);
            worksheet.Cells["N" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "SndVelD", "0")), 2);
            worksheet.Cells["O" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "AvgSndVel", "0")), 2);
            
            // P-S列：状态数据
            worksheet.Cells["P" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "StatusA", "0")), 2);
            worksheet.Cells["Q" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "StatusB", "0")), 2);
            worksheet.Cells["R" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "StatusC", "0")), 2);
            worksheet.Cells["S" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "StatusD", "0")), 2);
            
            // T-AA列：良好百分比数据
            worksheet.Cells["T" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "PctGoodA1", "0")), 2);
            worksheet.Cells["U" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "PctGoodA2", "0")), 2);
            worksheet.Cells["V" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "PctGoodB1", "0")), 2);
            worksheet.Cells["W" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "PctGoodB2", "0")), 2);
            worksheet.Cells["X" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "PctGoodC1", "0")), 2);
            worksheet.Cells["Y" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "PctGoodC2", "0")), 2);
            worksheet.Cells["Z" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "PctGoodD1", "0")), 2);
            worksheet.Cells["AA" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "PctGoodD2", "0")), 2);
            
            // AB-AI列：增益数据
            worksheet.Cells["AB" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "GainA1", "0")), 2);
            worksheet.Cells["AC" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "GainA2", "0")), 2);
            worksheet.Cells["AD" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "GainB1", "0")), 2);
            worksheet.Cells["AE" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "GainB2", "0")), 2);
            worksheet.Cells["AF" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "GainC1", "0")), 2);
            worksheet.Cells["AG" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "GainC2", "0")), 2);
            worksheet.Cells["AH" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "GainD1", "0")), 2);
            worksheet.Cells["AI" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "GainD2", "0")), 2);
            
            // AJ-AQ列：SNR数据
            worksheet.Cells["AJ" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "SNRA1", "0")), 2);
            worksheet.Cells["AK" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "SNRA2", "0")), 2);
            worksheet.Cells["AL" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "SNRB1", "0")), 2);
            worksheet.Cells["AM" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "SNRB2", "0")), 2);
            worksheet.Cells["AN" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "SNRC1", "0")), 2);
            worksheet.Cells["AO" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "SNRC2", "0")), 2);
            worksheet.Cells["AP" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "SNRD1", "0")), 2);
            worksheet.Cells["AQ" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "SNRD2", "0")), 2);
            
            // AV, AX, BC-BI列：其他参数
            worksheet.Cells["AV" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "LinearMtrFctr", "0")), 2);
            worksheet.Cells["AX" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "ProfileFactor", "0")), 4);
            worksheet.Cells["BC" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "Symmetry", "0")), 4);
            worksheet.Cells["BD" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "CrossFlow", "0")), 4);
            worksheet.Cells["BE" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "TurbulenceA", "0")), 2);
            worksheet.Cells["BF" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "TurbulenceB", "0")), 2);
            worksheet.Cells["BG" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "TurbulenceC", "0")), 2);
            worksheet.Cells["BH" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "TurbulenceD", "0")), 2);
            worksheet.Cells["BI" + rowNo].Value = Math.Round(ParseDoubleOrDefault(GetValueOrDefault(record, streamTablePrefix + "SwirlAngle", "0")), 2);
        }



        /// <summary>
        /// 解析数据字段的数值
        /// </summary>
        private double ParseDoubleValue(Dictionary<string, string> data, string key)
        {
            if (data.TryGetValue(key, out string value) && double.TryParse(value, out double result))
            {
                return result;
            }
            return 0.0;
        }

        /// <summary>
        /// 手动触发自动核查（用于测试）
        /// </summary>
        public async Task ExecuteManualCheckAsync()
        {
            Log.Info("Manual automatic check triggered");
            await ExecuteAutomaticCheckAsync();
        }

        /// <summary>
        /// 手动触发自动核查（用于测试，指定时间）
        /// </summary>
        /// <param name="testTime">指定的测试时间点</param>
        public async Task ExecuteManualCheckAsync(DateTime testTime)
        {
            Log.Info($"Manual automatic check triggered with specified time: {testTime:yyyy-MM-dd HH:mm:ss}");
            await ExecuteAutomaticCheckAsync(testTime);
        }

        /// <summary>
        /// 获取AGA10 DLL状态（用于调试）
        /// </summary>
        public static string GetAGA10Status()
        {
            lock (_aga10Lock)
            {
                try
                {
                    var initResult = AGA10_Init();
                    var uninitResult = AGA10_UnInit();
                    return $"AGA10 DLL Status - Init: {initResult}, UnInit: {uninitResult}";
                }
                catch (Exception ex)
                {
                    return $"AGA10 DLL Error: {ex.Message}";
                }
            }
        }

        #region IDisposable Implementation

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _dailyTimer?.Stop();
                    _dailyTimer?.Dispose();
                }
                
                _disposed = true;
            }
        }

        #endregion

        #region Helper Classes and Structures

        /// <summary>
        /// 设备组合信息
        /// </summary>
        private class DeviceCombination
        {
            public string Station { get; set; }
            public string FlowMeter { get; set; }
            public string FlowComputer { get; set; }
        }

        /// <summary>
        /// 计算结果
        /// </summary>
        private class CalculationResult
        {
            public double CalculatedSOS { get; set; }
            public double MeasuredSOS { get; set; }
            public double Deviation { get; set; }
            public bool IsValid { get; set; }
        }

        /// <summary>
        /// AGA10算法结构体
        /// </summary>
        public struct AGA10STRUCT
        {
            public int lStatus; // /* calculation status */
            public int bForceUpdate; // /* signal To perform full calculation */
            public double Methane;
            public double Nitrogen;
            public double CO2;
            public double Ethane;
            public double Propane;
            public double H2O;
            public double H2S;
            public double H2;
            public double CO;
            public double O2;
            public double i_Butane;
            public double n_Butane;
            public double i_Pentane;
            public double n_Pentane;
            public double n_Hexane;
            public double n_Heptane;
            public double n_Octane;
            public double n_Nonane;
            public double n_Decane;
            public double He;
            public double Ar;
            public double dPb; //* Contract base Pressure (Pa) */
            public double dTb; //* Contract base temperature (K) */
            public double dPf; //* Absolute Pressure (Pa) */
            public double dTf; //* Flowing temperature (K) */
            public double dMrx; //* mixture molar mass */
            public double dZb; //* compressibility at contract base condition */
            public double dZf; //* compressibility at flowing condition */
            public double dFpv; //* supercompressibility */
            public double dDb; //* molar density at contract base conditions (moles/dm3) */
            public double dDf; //* molar density at flowing conditions (moles/dm3) */
            public double dRhob; //* mass density at contract base conditions (kg/m3) */
            public double dRhof; //* mass density at flowing conditions (kg/m3) */
            public double dRD_Ideal; //* ideal gas relative density */
            public double dRD_Real; //* real gas relative density */
            public double dHo; //* ideal gas specific enthalpy */
            public double dH; //* real gas specific enthalpy (J/kg) */
            public double dS; //* real gas specific entropy (J/kg-mol.K)*/
            public double dCpi; //* ideal gas constant pressure heat capacity (J/kg-mol.K)*/
            public double dCp; //* real gas constant pressure heat capacity (J/kg-mol.K)*/
            public double dCv; //* real gas constant volume heat capacity (J/kg-mol.K)*/
            public double dk; //* ratio of specific heats */
            public double dKappa; //* isentropic exponent, denoted with Greek letter kappa */
            public double dSOS; //* speed of sound (m/s) */
            public double dCstar; //* critical flow factor C* */
        }

        #endregion
    }
} 
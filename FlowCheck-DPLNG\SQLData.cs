﻿using System;
using System.Data;
using System.Data.Odbc;
using System.Data.SqlClient;

namespace FlowCheck_DPLNG
{
    public class SQLData
    {
        private static string ip = string.Empty;
        private static string usercode = string.Empty;
        private static string userpwd = string.Empty;
        private static string sqlstr = string.Empty;
        private static string db = string.Empty;
        private static string times = string.Empty;
        private static string apisql = string.Empty;

        private static void dbConfig()
        {
            Cl_Base.getUserSet(out ip, out usercode, out userpwd, out db, out times);
        }

        public static OdbcConnection creatCon()
        {
            dbConfig();
            //string conStr = "Data Source = " + ip + ";Initial Catalog = " + db + ";User Id = " + usercode + ";Password = " + userpwd + ";";
            string conStr = "DSN=" + ip + ";uid=" + usercode + ";pwd=" + userpwd + "";
            OdbcConnection con = new OdbcConnection(conStr);
            return con;
        }

        public static DataSet getEditRows(string sqlStr, string sqltable) //执行SQL语句并返回表中的一行
        {
            DataSet ds = new DataSet();
            OdbcConnection con = creatCon();
            con.Open();
            OdbcDataAdapter sda = new OdbcDataAdapter(sqlStr, con);
            sda.Fill(ds, sqltable);
            con.Close();
            return ds;
        }

        public static DataTable GetRowsFromOE(string sqlStr, string sqltable)
        {
            DataSet ds;
            OdbcConnection con = creatCon();
            OdbcCommand com = new OdbcCommand(sqlStr, con);
            try
            {
                con.Open();
                OdbcDataAdapter sda = new OdbcDataAdapter(com);
                ds = new DataSet();
                sda.Fill(ds, sqltable);
                con.Close();
                return ds.Tables[sqltable];
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"GetRowsFromOE: Caught exception, ignoring: {ex.Message}");
                con.Close();
                return null;
            }
        }

        public static DataTable getRowsFromSQLServer(string sqlStr, string sqltable)
        {
            string DbAddress = ".";
            string DbName = "TianJingLNG_IOServer";
            string UserName = "sa";
            string Password = "Admin123";

            string conStr =
                "Data Source = "
                + DbAddress
                + ";Initial Catalog = "
                + DbName
                + ";User Id = "
                + UserName
                + ";Password = "
                + Password
                + ";";
            SqlConnection con = new SqlConnection(conStr);

            DataSet ds;
            SqlCommand com = new SqlCommand(sqlStr, con);
            try
            {
                con.Open();
            }
            catch (Exception ex)
            {
                Log.AddLog("Connect to Database error:" + ex.Message);
                return null;
            }

            SqlDataAdapter sda = new SqlDataAdapter(com);
            ds = new DataSet();
            sda.Fill(ds, sqltable);
            con.Close();
            return ds.Tables[sqltable];
        }

        public static object execSca1(string sqlStr) //返回第一行第一列
        {
            string objName = "";
            OdbcConnection con = creatCon();
            con.Open();
            OdbcCommand com = new OdbcCommand(sqlStr, con);
            object obj = com.ExecuteScalar();
            con.Close();
            if (obj == null)
            {
                objName = "";
            }
            else
            {
                objName = obj.ToString();
            }

            return objName;
        }

        public static bool execSql(string sqlStr) //执行OE数据表的添加、删除和更新操作
        {
            OdbcConnection con = creatCon();
            con.Open();
            OdbcCommand com = new OdbcCommand(sqlStr, con);
            try
            {
                if (com.ExecuteNonQuery() > 0)
                {
                    con.Close();
                    return true;
                }
                else
                {
                    con.Close();
                    return false;
                }
            }
            catch
            {
                return false;
            }
        }

        public static bool _execSql(string sqlStr) //执行SQL SERVER数据表的添加、删除和更新操作
        {
            string DbAddress = "*********";
            string DbName = "db_load";
            string UserName = "sa";
            string Password = "Welcome1";

            string conStr =
                "Data Source = "
                + DbAddress
                + ";Initial Catalog = "
                + DbName
                + ";User Id = "
                + UserName
                + ";Password = "
                + Password
                + ";";
            SqlConnection con = new SqlConnection(conStr);
            con.Open();
            SqlCommand com = new SqlCommand(sqlStr, con);

            try
            {
                if (com.ExecuteNonQuery() > 0)
                {
                    con.Close();
                    return true;
                }
                else
                {
                    con.Close();
                    return false;
                }
            }
            catch
            {
                return false;
            }
        }
    }
}